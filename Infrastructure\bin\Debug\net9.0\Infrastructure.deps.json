{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Infrastructure/1.0.0": {"dependencies": {"Domain": "1.0.0", "Microsoft.EntityFrameworkCore": "9.0.7", "Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.DependencyInjection": "9.0.7", "MySql.Data.EntityFrameworkCore": "8.0.22", "MySqlConnector": "2.4.0", "System.Reflection": "4.3.0"}, "runtime": {"Infrastructure.dll": {}}}, "BouncyCastle.NetCore/1.8.3": {"dependencies": {"NETStandard.Library": "1.6.0", "System.Reflection": "4.3.0", "System.Reflection.TypeExtensions": "4.1.0"}, "runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "1.8.2.0", "fileVersion": "1.8.18099.1"}}}, "Google.Protobuf/3.11.4": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/Google.Protobuf.dll": {"assemblyVersion": "3.11.4.0", "fileVersion": "3.11.4.0"}}}, "K4os.Compression.LZ4/1.1.11": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "1.1.11.0", "fileVersion": "1.1.11.0"}}}, "K4os.Compression.LZ4.Streams/1.1.11": {"dependencies": {"K4os.Compression.LZ4": "1.1.11", "K4os.Hash.xxHash": "1.0.6"}, "runtime": {"lib/netstandard2.0/K4os.Compression.LZ4.Streams.dll": {"assemblyVersion": "1.1.11.0", "fileVersion": "1.1.11.0"}}}, "K4os.Hash.xxHash/1.0.6": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/K4os.Hash.xxHash.dll": {"assemblyVersion": "1.0.6.0", "fileVersion": "1.0.6.0"}}}, "Microsoft.CSharp/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}}, "Microsoft.EntityFrameworkCore/9.0.7": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.7", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {}, "Microsoft.EntityFrameworkCore.Relational/3.1.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.7"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.100.119.61403"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Primitives/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.2.0", "fileVersion": "4.700.19.56404"}}}, "MySql.Data/8.0.22": {"dependencies": {"BouncyCastle.NetCore": "1.8.3", "Google.Protobuf": "3.11.4", "K4os.Compression.LZ4": "1.1.11", "K4os.Compression.LZ4.Streams": "1.1.11", "K4os.Hash.xxHash": "1.0.6", "SSH.NET": "2016.1.0", "System.Buffers": "4.5.1", "System.Configuration.ConfigurationManager": "4.4.1", "System.Security.Permissions": "4.7.0", "System.Text.Encoding.CodePages": "4.4.0"}, "runtime": {"lib/net5.0/MySql.Data.dll": {"assemblyVersion": "8.0.22.0", "fileVersion": "8.0.22.0"}, "lib/net5.0/Ubiety.Dns.Core.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}, "lib/net5.0/Zstandard.Net.dll": {"assemblyVersion": "1.1.7.0", "fileVersion": "1.1.7.0"}}}, "MySql.Data.EntityFrameworkCore/8.0.22": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "3.1.1", "MySql.Data": "8.0.22"}, "runtime": {"lib/netstandard2.1/MySql.Data.EntityFrameworkCore.dll": {"assemblyVersion": "8.0.22.0", "fileVersion": "8.0.22.0"}}}, "MySqlConnector/2.4.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/MySqlConnector.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.4.0.0"}}}, "NETStandard.Library/1.6.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.Primitives": "4.0.1", "System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.3.0", "System.IO.Compression": "4.1.0", "System.IO.Compression.ZipFile": "4.0.1", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.Sockets": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.0.1", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}}, "runtime.native.System/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "SSH.NET/2016.1.0": {"dependencies": {"Microsoft.CSharp": "4.0.1", "SshNet.Security.Cryptography": "1.2.0", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Diagnostics.TraceSource": "4.0.0", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Linq": "4.1.0", "System.Net.NameResolution": "4.0.0", "System.Net.Sockets": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10", "System.Threading.Timer": "4.0.1", "System.Xml.XPath.XmlDocument": "4.0.1", "System.Xml.XmlDocument": "4.0.1"}, "runtime": {"lib/netstandard1.3/Renci.SshNet.dll": {"assemblyVersion": "2016.1.0.0", "fileVersion": "2016.1.0.0"}}}, "SshNet.Security.Cryptography/1.2.0": {"dependencies": {"System.IO": "4.3.0", "System.Security.Cryptography.Primitives": "4.0.0"}, "runtime": {"lib/netstandard1.3/SshNet.Security.Cryptography.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.1": {}, "System.Collections/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.3.0"}}, "System.Configuration.ConfigurationManager/4.4.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.4.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.6.25921.2"}}}, "System.Console/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.0.11"}}, "System.Diagnostics.Tools/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Diagnostics.Tracing/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {"assemblyVersion": "4.0.0.1", "fileVersion": "4.6.26919.2"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "4.0.2.0", "fileVersion": "4.700.19.56404"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.2.0", "fileVersion": "4.700.19.56404"}}}, "System.Dynamic.Runtime/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Globalization/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.0.11", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.0.0", "runtime.native.System.IO.Compression": "4.1.0"}}, "System.IO.Compression.ZipFile/4.0.1": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.0.1": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Linq.Expressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Memory/4.5.3": {}, "System.Net.Http/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.3.0", "System.IO.FileSystem": "4.0.1", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Net.NameResolution/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Principal.Windows": "4.7.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.0.0"}}, "System.Net.Primitives/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.0.1"}}, "System.Net.Sockets/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.0.11", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Threading": "4.0.11"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.0.1": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.0.1": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.0.1": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.0.11", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.0.1"}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Runtime.Numerics/4.0.1": {"dependencies": {"System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Cryptography.Algorithms/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.0.11", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Cng/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.0.11"}}, "System.Security.Cryptography.Encoding/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.OpenSsl/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Cryptography.Primitives/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.4.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}}, "System.Security.Cryptography.X509Certificates/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.3.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "System.Text.Encoding.Extensions/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.RegularExpressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Threading/4.0.11": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.0.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading.ThreadPool/4.0.10": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.0.1"}}, "System.Threading.Timer/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Xml.ReaderWriter/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.0.0"}}, "System.Xml.XDocument/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "System.Xml.XmlDocument/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "System.Xml.XPath/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "System.Xml.XPath.XmlDocument/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XPath": "4.0.1", "System.Xml.XmlDocument": "4.0.1"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24212.1"}}}, "Domain/1.0.0": {"runtime": {"Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BouncyCastle.NetCore/1.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-jAy3uHN1U9DpoT/TgLqDTEhlccn/4doOVxhSmmNsnoSsGfHT9Lwr634ac9D0YFujAhNw61nWF4UIpHyzv0aQww==", "path": "bouncycastle.netcore/1.8.3", "hashPath": "bouncycastle.netcore.1.8.3.nupkg.sha512"}, "Google.Protobuf/3.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-dajCxjDCiPyZuqwZCkFJTwhn/0TJ5VesIs4fXvs56ez1VUi68JjhYMMsPjnJ9gcPqJwTMtXMU1WqUdXYiG1x4w==", "path": "google.protobuf/3.11.4", "hashPath": "google.protobuf.3.11.4.nupkg.sha512"}, "K4os.Compression.LZ4/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-RNvJw0UGkedPhCqVBNIogtfQebY+bQt0PN7xDbVe5LWLra0ZEqPfjPSl7iStj3rgDnkqkkTTpm+vCX3hU1qKmA==", "path": "k4os.compression.lz4/1.1.11", "hashPath": "k4os.compression.lz4.1.1.11.nupkg.sha512"}, "K4os.Compression.LZ4.Streams/1.1.11": {"type": "package", "serviceable": true, "sha512": "sha512-x+Bid<PERSON>riYsNP4HNTHKx+5cVQguHHwbfs6nM79fDHOCOrcNwnaBms4dwzAV/ZALmKsNKcHmY74PeUZiCC4qLKwQ==", "path": "k4os.compression.lz4.streams/1.1.11", "hashPath": "k4os.compression.lz4.streams.1.1.11.nupkg.sha512"}, "K4os.Hash.xxHash/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-jCfNP0inx1sGcP3KSbpiDEH3km2e1sVBjMfKo+V92jr1dL4ZYgA1uhRMl1wAtdGZcbObXIikKqtVlgx3j/CW6g==", "path": "k4os.hash.xxhash/1.0.6", "hashPath": "k4os.hash.xxhash.1.0.6.nupkg.sha512"}, "Microsoft.CSharp/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-17h8b5mXa87XYKrrVqdgZ38JefSUqLChUQpXgSnpzsM0nDOhE40FTeNWOJ/YmySGV6tG6T8+hjz6vxbknHJr6A==", "path": "microsoft.csharp/4.0.1", "hashPath": "microsoft.csharp.4.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-PbD0q5ax15r91jD4TN7xbDCjldZSz4JfpYN4ZZjAkWeUyROkV92Ydg0O2/1keFA+2u3KPsDkJMmBKv2zQ06ZVg==", "path": "microsoft.entityframeworkcore/9.0.7", "hashPath": "microsoft.entityframeworkcore.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-YUXNerEkCf4OANO+zjuMznpUW7R8XxSCqmBfYhBrbrJVc09i84KkNgeUTaOUXCGogSK/3d7ORRhMqfUobnejBg==", "path": "microsoft.entityframeworkcore.abstractions/9.0.7", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-HqiPjAvjVOsyA1svnjL81/Wk2MRQYMK/lxKVWvw0f5IcA//VcxBepVSAqe7CFirdsPXqe8rFKEwZROWZTz7Jqw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.7", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-iBt59oeV97kqI6ZkdSpnUV1cC+hCGf5xqot331waiF5d5sxksLJZghkUZOLpe4yuPg3mnngXD6bPqMsLa4II5g==", "path": "microsoft.entityframeworkcore.relational/3.1.1", "hashPath": "microsoft.entityframeworkcore.relational.3.1.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-30necCQehcg9lFkMEIE7HczcoYGML8GUH6jlincA18d896fLZM9wl5tpTPJHgzANQE/6KXRLZSWbgevgg5csSw==", "path": "microsoft.extensions.caching.abstractions/9.0.7", "hashPath": "microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-nDu6c8fwrHQYccLnWnvyElrdkL3rZ97TZNqL+niMFUcApVBHdpDmKcRvciGymJ4Y0iLDTOo5J2XhDQEbNb+dFg==", "path": "microsoft.extensions.caching.memory/9.0.7", "hashPath": "microsoft.extensions.caching.memory.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-oxGR51+w5cXm5B9gU6XwpAB2sTiyPSmZm7hjvv0rzRnmL5o/KZzE103AuQj7sK26OBupjVzU/bZxDWvvU4nhEg==", "path": "microsoft.extensions.configuration/9.0.7", "hashPath": "microsoft.extensions.configuration.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "path": "microsoft.extensions.dependencyinjection/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "path": "microsoft.extensions.logging/9.0.7", "hashPath": "microsoft.extensions.logging.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "path": "microsoft.extensions.logging.abstractions/9.0.7", "hashPath": "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "path": "microsoft.extensions.options/9.0.7", "hashPath": "microsoft.extensions.options.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "path": "microsoft.extensions.primitives/9.0.7", "hashPath": "microsoft.extensions.primitives.9.0.7.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA==", "path": "microsoft.win32.primitives/4.0.1", "hashPath": "microsoft.win32.primitives.4.0.1.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "MySql.Data/8.0.22": {"type": "package", "serviceable": true, "sha512": "sha512-DTKXORgTwQLbAjl8g5+YhvrAB+peQLfAZQ7gbm5Ejma37f/vBtZqP5aTnqZoJUzaQQgj4Psgdcnlo6IwYedyCg==", "path": "mysql.data/8.0.22", "hashPath": "mysql.data.8.0.22.nupkg.sha512"}, "MySql.Data.EntityFrameworkCore/8.0.22": {"type": "package", "serviceable": true, "sha512": "sha512-o/+OMqQ0fw4f6COfRrbwqP0CKcqoFkCfb1Rl3mfgUcae3XqAkiA6t8rsjA1wnkxUvwfNYX+YM2w2Xa6u4QEUkA==", "path": "mysql.data.entityframeworkcore/8.0.22", "hashPath": "mysql.data.entityframeworkcore.8.0.22.nupkg.sha512"}, "MySqlConnector/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-78M+gVOjbdZEDIyXQqcA7EYlCGS3tpbUELHvn6638A2w0pkPI625ixnzsa5staAd3N9/xFmPJtkKDYwsXpFi/w==", "path": "mysqlconnector/2.4.0", "hashPath": "mysqlconnector.2.4.0.nupkg.sha512"}, "NETStandard.Library/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ypsCvIdCZ4IoYASJHt6tF2fMo7N30NLgV1EbmC+snO490OMl9FvVxmumw14rhReWU3j3g7BYudG6YCrchwHJlA==", "path": "netstandard.library/1.6.0", "hashPath": "netstandard.library.1.6.0.nupkg.sha512"}, "runtime.native.System/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "path": "runtime.native.system/4.0.0", "hashPath": "runtime.native.system.4.0.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob7nvnJBox1aaB222zSVZSkf4WrebPG4qFscfK7vmD7P7NxoSxACQLtO7ytWpqXDn2wcd/+45+EAZ7xjaPip8A==", "path": "runtime.native.system.io.compression/4.1.0", "hashPath": "runtime.native.system.io.compression.4.1.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Nh0UPZx2Vifh8r+J+H2jxifZUD3sBrmolgiFWJd2yiNrxO0xTa6bAw3YwRn1VOiSen/tUXMS31ttNItCZ6lKuA==", "path": "runtime.native.system.net.http/4.0.1", "hashPath": "runtime.native.system.net.http.4.0.1.nupkg.sha512"}, "runtime.native.System.Security.Cryptography/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2CQK0jmO6Eu7ZeMgD+LOFbNJSXHFVQbCJJkEyEwowh1SCgYnrn9W9RykMfpeeVGw7h4IBvYikzpGUlmZTUafJw==", "path": "runtime.native.system.security.cryptography/4.0.0", "hashPath": "runtime.native.system.security.cryptography.4.0.0.nupkg.sha512"}, "SSH.NET/2016.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-b0fcFFOE044KNRhq1uGujmK04ab+OzA9xARdeVCoZrY6I4D1IIaR6dn6qBIa+er4bJapGBhznDitwcRQpSRC0w==", "path": "ssh.net/2016.1.0", "hashPath": "ssh.net.2016.1.0.nupkg.sha512"}, "SshNet.Security.Cryptography/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EeFsirrrkIK+cdlYsxukNjBN98cbU7eHfTYZEwxsbOa3dvgR/OhOD06C0sTxvNPhe4UQ6yM0p1sKcT69jqgjTw==", "path": "sshnet.security.cryptography/1.2.0", "hashPath": "sshnet.security.cryptography.1.2.0.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "path": "system.collections/4.0.11", "hashPath": "system.collections.4.0.11.nupkg.sha512"}, "System.Collections.Concurrent/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-2gBcbb3drMLgxlI0fBfxMA31ec6AEyYCHygGse4vxceJan8mRIWeKJ24BFzN7+bi/NFTgdIgufzb94LWO5EERQ==", "path": "system.collections.concurrent/4.0.12", "hashPath": "system.collections.concurrent.4.0.12.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-jz3TWKMAeuDEyrPCK5Jyt4bzQcmzUIMcY9Ud6PkElFxTfnsihV+9N/UCqvxe1z5gc7jMYAnj7V1COMS9QKIuHQ==", "path": "system.configuration.configurationmanager/4.4.1", "hashPath": "system.configuration.configurationmanager.4.4.1.nupkg.sha512"}, "System.Console/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qSKUSOIiYA/a0g5XXdxFcUFmv1hNICBD7QZ0QhGYVipPIhvpiydY8VZqr1thmCXvmn8aipMg64zuanB4eotK9A==", "path": "system.console/4.0.0", "hashPath": "system.console.4.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw==", "path": "system.diagnostics.debug/4.0.11", "hashPath": "system.diagnostics.debug.4.0.11.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YKglnq4BMTJxfcr6nuT08g+yJ0UxdePIHxosiLuljuHIUR6t4KhFsyaHOaOc1Ofqp0PUvJ0EmcgiEz6T7vEx3w==", "path": "system.diagnostics.diagnosticsource/4.0.0", "hashPath": "system.diagnostics.diagnosticsource.4.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g==", "path": "system.diagnostics.tools/4.0.1", "hashPath": "system.diagnostics.tools.4.0.1.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6WVCczFZKXwpWpzd/iJkYnsmWTSFFiU24Xx/YdHXBcu+nFI/ehTgeqdJQFbtRPzbrO3KtRNjvkhtj4t5/WwWsA==", "path": "system.diagnostics.tracesource/4.0.0", "hashPath": "system.diagnostics.tracesource.4.0.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vDN1PoMZCkkdNjvZLql592oYJZgS7URcJzJ7bxeBgGtx5UtR5leNm49VmfHGqIffX4FKacHbI3H6UyNSHQknBg==", "path": "system.diagnostics.tracing/4.1.0", "hashPath": "system.diagnostics.tracing.4.1.0.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "path": "system.dynamic.runtime/4.0.11", "hashPath": "system.dynamic.runtime.4.0.11.nupkg.sha512"}, "System.Globalization/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg==", "path": "system.globalization/4.0.11", "hashPath": "system.globalization.4.0.11.nupkg.sha512"}, "System.Globalization.Calendars/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L1c6IqeQ88vuzC1P81JeHmHA8mxq8a18NUBNXnIY/BVb+TCyAaGIFbhpZt60h9FJNmisymoQkHEFSE9Vslja1Q==", "path": "system.globalization.calendars/4.0.1", "hashPath": "system.globalization.calendars.4.0.1.nupkg.sha512"}, "System.Globalization.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KKo23iKeOaIg61SSXwjANN7QYDr/3op3OWGGzDzz7mypx0Za0fZSeG0l6cco8Ntp8YMYkIQcAqlk8yhm5/Uhcg==", "path": "system.globalization.extensions/4.0.1", "hashPath": "system.globalization.extensions.4.0.1.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TjnBS6eztThSzeSib+WyVbLzEdLKUcEHN69VtS3u8aAsSc18FU6xCZlNWWsEd8SKcXAE+y1sOu7VbU8sUeM0sg==", "path": "system.io.compression/4.1.0", "hashPath": "system.io.compression.4.1.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hBQYJzfTbQURF10nLhd+az2NHxsU6MU7AB8RUf4IolBP5lOAm4Luho851xl+CqslmhI5ZH/el8BlngEk4lBkaQ==", "path": "system.io.compression.zipfile/4.0.1", "hashPath": "system.io.compression.zipfile.4.0.1.nupkg.sha512"}, "System.IO.FileSystem/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "path": "system.io.filesystem/4.0.1", "hashPath": "system.io.filesystem.4.0.1.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "path": "system.io.filesystem.primitives/4.0.1", "hashPath": "system.io.filesystem.primitives.4.0.1.nupkg.sha512"}, "System.Linq/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "path": "system.linq/4.1.0", "hashPath": "system.linq.4.1.0.nupkg.sha512"}, "System.Linq.Expressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "path": "system.linq.expressions/4.1.0", "hashPath": "system.linq.expressions.4.1.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Net.Http/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULq9g3SOPVuupt+Y3U+A37coXzdNisB1neFCSKzBwo182u0RDddKJF8I5+HfyXqK6OhJPgeoAwWXrbiUXuRDsg==", "path": "system.net.http/4.1.0", "hashPath": "system.net.http.4.1.0.nupkg.sha512"}, "System.Net.NameResolution/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JdqRdM1Qym3YehqdKIi5LHrpypP4JMfxKQSNCJ2z4WawkG0il+N3XfNeJOxll2XrTnG7WgYYPoeiu/KOwg0DQw==", "path": "system.net.nameresolution/4.0.0", "hashPath": "system.net.nameresolution.4.0.0.nupkg.sha512"}, "System.Net.Primitives/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-hVvfl4405DRjA2408luZekbPhplJK03j2Y2lSfMlny7GHXlkByw1iLnc9mgKW0GdQn73vvMcWrWewAhylXA4Nw==", "path": "system.net.primitives/4.0.11", "hashPath": "system.net.primitives.4.0.11.nupkg.sha512"}, "System.Net.Sockets/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-xAz0N3dAV/aR/9g8r0Y5oEqU1JRsz29F5EGb/WVHmX3jVSLqi2/92M5hTad2aNWovruXrJpJtgZ9fccPMG9uSw==", "path": "system.net.sockets/4.1.0", "hashPath": "system.net.sockets.4.1.0.nupkg.sha512"}, "System.ObjectModel/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "path": "system.objectmodel/4.0.12", "hashPath": "system.objectmodel.4.0.12.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "path": "system.reflection.emit/4.0.1", "hashPath": "system.reflection.emit.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "path": "system.reflection.emit.ilgeneration/4.0.1", "hashPath": "system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "path": "system.reflection.emit.lightweight/4.0.1", "hashPath": "system.reflection.emit.lightweight.4.0.1.nupkg.sha512"}, "System.Reflection.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "path": "system.reflection.extensions/4.0.1", "hashPath": "system.reflection.extensions.4.0.1.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "path": "system.reflection.typeextensions/4.1.0", "hashPath": "system.reflection.typeextensions.4.1.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA==", "path": "system.resources.resourcemanager/4.0.1", "hashPath": "system.resources.resourcemanager.4.0.1.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ==", "path": "system.runtime.extensions/4.1.0", "hashPath": "system.runtime.extensions.4.1.0.nupkg.sha512"}, "System.Runtime.Handles/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "path": "system.runtime.handles/4.0.1", "hashPath": "system.runtime.handles.4.0.1.nupkg.sha512"}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "path": "system.runtime.interopservices/4.1.0", "hashPath": "system.runtime.interopservices.4.1.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Runtime.Numerics/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+XbKFuzdmLP3d1o9pdHu2nxjNr2OEPqGzKeegPLCUMM71a0t50A/rOcIRmGs9wR7a8KuHX6hYs/7/TymIGLNqg==", "path": "system.runtime.numerics/4.0.1", "hashPath": "system.runtime.numerics.4.0.1.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-8JQFxbLVdrtIOKMDN38Fn0GWnqYZw/oMlwOUG/qz1jqChvyZlnUmu+0s7wLx7JYua/nAXoESpHA3iw11QFWhXg==", "path": "system.security.cryptography.algorithms/4.2.0", "hashPath": "system.security.cryptography.algorithms.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cUJ2h+ZvONDe28Szw3st5dOHdjndhJzQ2WObDEXAWRPEQBtVItVoxbXM/OEsTthl3cNn2dk2k0I3y45igCQcLw==", "path": "system.security.cryptography.cng/4.2.0", "hashPath": "system.security.cryptography.cng.4.2.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/i1Usuo4PgAqgbPNC0NjbO3jPW//BoBlTpcWFD1EHVbidH21y4c1ap5bbEMSGAXjAShhMH4abi/K8fILrnu4BQ==", "path": "system.security.cryptography.csp/4.0.0", "hashPath": "system.security.cryptography.csp.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FbKgE5MbxSQMPcSVRgwM6bXN3GtyAh04NkV8E5zKCBE26X0vYW0UtTa2FIgkH33WVqBVxRgxljlVYumWtU+HcQ==", "path": "system.security.cryptography.encoding/4.0.0", "hashPath": "system.security.cryptography.encoding.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HUG/zNUJwEiLkoURDixzkzZdB5yGA5pQhDP93ArOpDPQMteURIGERRNzzoJlmTreLBWr5lkFSjjMSk8ySEpQMw==", "path": "system.security.cryptography.openssl/4.0.0", "hashPath": "system.security.cryptography.openssl.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wkd7QryWYjkQclX0bngpntW5HSlMzeJU24UaLJQ7YTfI8ydAVAaU2J+HXLLABOVJlKTVvAeL0Aj39VeTe7L+oA==", "path": "system.security.cryptography.primitives/4.0.0", "hashPath": "system.security.cryptography.primitives.4.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cJV7ScGW7EhatRsjehfvvYVBvtiSMKgN8bOVI0bQhnF5bU7vnHVIsH49Kva7i7GWaWYvmEzkYVk1TC+gZYBEog==", "path": "system.security.cryptography.protecteddata/4.4.0", "hashPath": "system.security.cryptography.protecteddata.4.4.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4HEfsQIKAhA1+ApNn729Gi09zh+lYWwyIuViihoMDWp1vQnEkL2ct7mAbhBlLYm+x/L4Rr/pyGge1lIY635e0w==", "path": "system.security.cryptography.x509certificates/4.1.0", "hashPath": "system.security.cryptography.x509certificates.4.1.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-6J<PERSON><PERSON>ZdaceBiLKLkYt8zJcp4xTJd1uYyXXEkPw6mnlUIjh1gZPIVKPtRXPmY5kLf6DwZmf5YLwR3QUrRonl7l0A==", "path": "system.text.encoding.codepages/4.4.0", "hashPath": "system.text.encoding.codepages.4.4.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ==", "path": "system.text.encoding.extensions/4.0.11", "hashPath": "system.text.encoding.extensions.4.0.11.nupkg.sha512"}, "System.Text.RegularExpressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-i88YCXpRTjCnoSQZtdlHkAOx4KNNik4hMy83n0+Ftlb7jvV6ZiZWMpnEZHhjBp6hQVh8gWd/iKNPzlPF7iyA2g==", "path": "system.text.regularexpressions/4.1.0", "hashPath": "system.text.regularexpressions.4.1.0.nupkg.sha512"}, "System.Threading/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ==", "path": "system.threading/4.0.11", "hashPath": "system.threading.4.0.11.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pH4FZDsZQ/WmgJtN4LWYmRdJAEeVkyriSwrv2Teoe5FOU0Yxlb6II6GL8dBPOfRmutHGATduj3ooMt7dJ2+i+w==", "path": "system.threading.tasks.extensions/4.0.0", "hashPath": "system.threading.tasks.extensions.4.0.0.nupkg.sha512"}, "System.Threading.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gIdJqDXlOr5W9zeqFErLw3dsOsiShSCYtF9SEHitACycmvNvY8odf9kiKvp6V7aibc8C4HzzNBkWXjyfn7plbQ==", "path": "system.threading.thread/4.0.0", "hashPath": "system.threading.thread.4.0.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-IMXgB5Vf/5Qw1kpoVgJMOvUO1l32aC+qC3OaIZjWJOjvcxuxNWOK2ZTWWYXfij22NHxT2j1yWX5vlAeQWld9vA==", "path": "system.threading.threadpool/4.0.10", "hashPath": "system.threading.threadpool.4.0.10.nupkg.sha512"}, "System.Threading.Timer/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-saGfUV8uqVW6LeURiqxcGhZ24PzuRNaUBtbhVeuUAvky1naH395A/1nY0P2bWvrw/BreRtIB/EzTDkGBpqCwEw==", "path": "system.threading.timer/4.0.1", "hashPath": "system.threading.timer.4.0.1.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZIiLPsf67YZ9zgr31vzrFaYQqxRPX9cVHjtPSnmx4eN6lbS/yEyYNr2vs1doGDEscF0tjCZFsk9yUg1sC9e8tg==", "path": "system.xml.readerwriter/4.0.11", "hashPath": "system.xml.readerwriter.4.0.11.nupkg.sha512"}, "System.Xml.XDocument/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg==", "path": "system.xml.xdocument/4.0.11", "hashPath": "system.xml.xdocument.4.0.11.nupkg.sha512"}, "System.Xml.XmlDocument/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2eZu6IP+etFVBBFUFzw2w6J21DqIN5eL9Y8r8JfJWUmV28Z5P0SNU01oCisVHQgHsDhHPnmq2s1hJrJCFZWloQ==", "path": "system.xml.xmldocument/4.0.1", "hashPath": "system.xml.xmldocument.4.0.1.nupkg.sha512"}, "System.Xml.XPath/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UWd1H+1IJ9Wlq5nognZ/XJdyj8qPE4XufBUkAW59ijsCPjZkZe0MUzKKJFBr+ZWBe5Wq1u1d5f2CYgE93uH7DA==", "path": "system.xml.xpath/4.0.1", "hashPath": "system.xml.xpath.4.0.1.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zm2BdeanuncYs3NhCj4c9e1x3EXFzFBVv2wPEc/Dj4ZbI9R8ecLSR5frAsx4zJCPBtKQreQ7Q/KxJEohJZbfzA==", "path": "system.xml.xpath.xmldocument/4.0.1", "hashPath": "system.xml.xpath.xmldocument.4.0.1.nupkg.sha512"}, "Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}