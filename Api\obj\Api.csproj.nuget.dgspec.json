{"format": 1, "restore": {"c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Api\\Api.csproj": {}}, "projects": {"c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Api\\Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Api\\Api.csproj", "projectName": "Api", "projectPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Api\\Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Common\\Common.csproj": {"projectPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Common\\Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj", "projectName": "Application", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj": {"projectPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Common\\Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Common\\Common.csproj", "projectName": "Common", "projectPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Common\\Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Common\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Application\\Application.csproj"}, "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj": {"projectPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Infrastructure\\Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Infrastructure\\Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Infrastructure\\Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Infrastructure\\Infrastructure.csproj", "projectName": "Infrastructure", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Infrastructure\\Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj": {"projectPath": "c:\\Users\\<USER>\\OneDrive\\Desktop\\Projeto HC\\hcagents-solution\\Domain\\Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}, "MySql.Data.EntityFrameworkCore": {"target": "Package", "version": "[8.0.22, )"}, "MySqlConnector": {"target": "Package", "version": "[2.4.0, )"}, "System.Reflection": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}